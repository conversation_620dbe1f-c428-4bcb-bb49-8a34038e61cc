package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ConflictException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.auth.repository.UserRepository;
import com.collabhub.be.modules.auth.dto.HubAccessRedirectContext;
import com.collabhub.be.modules.auth.service.EmailService;
import com.collabhub.be.modules.auth.service.ExternalUserMagicLinkService;
import com.collabhub.be.modules.brands.repository.BrandContactRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.dto.HubParticipantInviteResponse;
import com.collabhub.be.modules.collaborationhub.dto.ParticipantInviteItem;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.notifications.repository.NotificationPreferenceRepository;
import com.collabhub.be.modules.notifications.dto.UnifiedNotificationRecipient;
import com.collabhub.be.modules.notifications.dto.InviteToHubNotification;
import com.collabhub.be.modules.notifications.dto.NotificationMetadata;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.dto.NotificationUrgency;
import com.collabhub.be.modules.notifications.service.NotificationDispatcherService;
import com.collabhub.be.modules.notifications.service.NotificationStorageService;
import org.jooq.generated.tables.pojos.User;
import com.collabhub.be.modules.notifications.service.NotificationPreferenceService;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.BrandContact;
import org.jooq.generated.tables.pojos.CollaborationHub;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.collabhub.be.modules.auth.constants.JwtClaims.*;

/**
 * Service for handling hub participant invitations.
 * Manages invitation logic for internal users, external users, and brand contacts.
 */
@Service
public class HubParticipantInvitationService {

    private static final Logger logger = LoggerFactory.getLogger(HubParticipantInvitationService.class);

    // Configuration Constants
    private static final int MAX_BATCH_INVITE_SIZE = 50;

    // Error Messages
    private static final String USER_NOT_FOUND_MESSAGE = "User not found or not in same account: ";
    private static final String BRAND_CONTACT_NOT_FOUND_MESSAGE = "Brand contact not found: ";
    private static final String PARTICIPANT_ALREADY_EXISTS_MESSAGE = "Participant already exists in this hub";
    private static final String EMAIL_ALREADY_EXISTS_MESSAGE = "Email is already a participant in this hub";
    private static final String INVALID_PARTICIPANT_TYPE_MESSAGE = "Invalid participant type: ";
    private static final String USER_ID_REQUIRED_MESSAGE = "User ID is required for internal participants";
    private static final String EMAIL_REQUIRED_MESSAGE = "Email is required for external participants";
    private static final String NAME_REQUIRED_MESSAGE = "Name is required for external participants";
    private static final String BRAND_CONTACT_ID_REQUIRED_MESSAGE = "Brand contact ID is required for brand contact participants";

    // Log Messages
    private static final String INVITING_PARTICIPANTS_LOG = "Inviting {} participants to hub {} by user {}";
    private static final String REACTIVATED_INTERNAL_PARTICIPANT_LOG = "Reactivated internal participant {} in hub {}";
    private static final String REACTIVATED_EXTERNAL_PARTICIPANT_LOG = "Reactivated external participant {} in hub {}";
    private static final String REACTIVATED_BRAND_CONTACT_PARTICIPANT_LOG = "Reactivated brand contact participant {} in hub {}";

    private final HubParticipantRepositoryImpl participantRepository;
    private final UserRepository userRepository;
    private final BrandContactRepositoryImpl brandContactRepository;
    private final ExternalUserMagicLinkService magicLinkService;
    private final EmailService emailService;
    private final NotificationDispatcherService notificationDispatcherService;
    private final NotificationPreferenceService notificationPreferenceService;
    private final NotificationPreferenceRepository notificationPreferenceRepository;

    public HubParticipantInvitationService(HubParticipantRepositoryImpl participantRepository,
                                         UserRepository userRepository,
                                         BrandContactRepositoryImpl brandContactRepository,
                                         ExternalUserMagicLinkService magicLinkService,
                                         EmailService emailService,
                                         NotificationDispatcherService notificationDispatcherService,
                                         NotificationPreferenceService notificationPreferenceService,
                                         NotificationPreferenceRepository notificationPreferenceRepository) {
        this.participantRepository = participantRepository;
        this.userRepository = userRepository;
        this.brandContactRepository = brandContactRepository;
        this.magicLinkService = magicLinkService;
        this.emailService = emailService;
        this.notificationDispatcherService = notificationDispatcherService;
        this.notificationPreferenceService = notificationPreferenceService;
        this.notificationPreferenceRepository = notificationPreferenceRepository;
    }

    /**
     * Processes all participant invitations for a hub.
     *
     * @param hub the collaboration hub
     * @param request the invitation request
     * @param accountId the account ID
     * @param invitationContext the invitation context
     * @return list of invited participants
     */
    @Transactional
    public List<HubParticipantInviteResponse.InvitedParticipant> processAllInvitations(
            CollaborationHub hub, 
            com.collabhub.be.modules.collaborationhub.dto.HubParticipantInviteRequest request,
            Long accountId,
            InvitationContext invitationContext) {

        logger.info(INVITING_PARTICIPANTS_LOG, request.getParticipants().size(), hub.getId(), invitationContext);

        if (request.getParticipants().size() > MAX_BATCH_INVITE_SIZE) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, 
                    "Cannot invite more than " + MAX_BATCH_INVITE_SIZE + " participants at once");
        }

        List<HubParticipantInviteResponse.InvitedParticipant> invitedParticipants = new ArrayList<>();

        for (ParticipantInviteItem item : request.getParticipants()) {
            try {
                HubParticipantInviteResponse.InvitedParticipant invitedParticipant = 
                        processParticipantInvitation(hub, item, accountId, invitationContext);
                invitedParticipants.add(invitedParticipant);
            } catch (Exception e) {
                logger.warn("Failed to invite participant {}: {}", item, e.getMessage());
                // Continue with other invitations - don't fail the entire batch
            }
        }

        return invitedParticipants;
    }

    /**
     * Processes a single participant invitation.
     *
     * @param hub the collaboration hub
     * @param item the participant invite item
     * @param accountId the account ID
     * @param invitationContext the invitation context
     * @return the invited participant response
     */
    @Transactional
    public HubParticipantInviteResponse.InvitedParticipant processParticipantInvitation(
            CollaborationHub hub, ParticipantInviteItem item, Long accountId, InvitationContext invitationContext) {

        return switch (item.getType()) {
            case INTERNAL -> inviteInternalUser(hub, item, accountId, invitationContext);
            case EXTERNAL -> inviteExternalUser(hub, item, accountId, invitationContext);
            case BRAND_CONTACT -> inviteBrandContact(hub, item, accountId, invitationContext);
            default -> throw new BadRequestException(ErrorCode.INVALID_INPUT, 
                    INVALID_PARTICIPANT_TYPE_MESSAGE + item.getType());
        };
    }

    /**
     * Invites an internal user to the hub.
     */
    @Transactional
    public HubParticipantInviteResponse.InvitedParticipant inviteInternalUser(
            CollaborationHub hub, ParticipantInviteItem item, Long accountId, InvitationContext invitationContext) {
        
        validateInternalUserRequest(item);
        
        // Check if user already exists as participant
        HubParticipant existingParticipant = findRemovedParticipant(hub.getId(), item.getEmail());
        if (existingParticipant != null) {
            return reactivateInternalParticipant(existingParticipant, item.getRole(), hub.getName(), invitationContext);
        }
        
        // Validate user doesn't already exist as active participant
        validateUserNotAlreadyParticipant(hub.getId(), item.getUserId());
        
        // Find and validate user
        User user = findAndValidateUser(item.getUserId(), accountId);
        
        return createNewInternalParticipant(hub, user, item.getRole(), invitationContext);
    }

    /**
     * Invites an external user to the hub.
     */
    @Transactional
    public HubParticipantInviteResponse.InvitedParticipant inviteExternalUser(
            CollaborationHub hub, ParticipantInviteItem item, Long accountId, InvitationContext invitationContext) {
        
        validateExternalUserRequest(item);
        
        // Check if email already exists as participant
        HubParticipant existingParticipant = findRemovedParticipantByEmail(hub.getId(), item.getEmail());
        if (existingParticipant != null) {
            return reactivateExternalParticipant(existingParticipant, item.getRole(), hub.getName(), accountId);
        }
        
        // Validate email doesn't already exist as active participant
        validateEmailNotAlreadyParticipant(hub.getId(), item.getEmail());
        
        return createNewExternalParticipant(hub, item, accountId, invitationContext);
    }

    /**
     * Invites a brand contact to the hub.
     */
    @Transactional
    public HubParticipantInviteResponse.InvitedParticipant inviteBrandContact(
            CollaborationHub hub, ParticipantInviteItem item, Long accountId, InvitationContext invitationContext) {
        
        validateBrandContactRequest(item);
        
        // Find and validate brand contact
        BrandContact brandContact = findAndValidateBrandContact(item.getBrandContactId(), hub.getBrandId(), accountId);
        
        // Check if brand contact already exists as participant
        HubParticipant existingParticipant = findRemovedParticipantByEmail(hub.getId(), brandContact.getEmail());
        if (existingParticipant != null) {
            return reactivateBrandContactParticipant(existingParticipant, item.getRole(), hub.getName(), accountId);
        }
        
        // Validate email doesn't already exist as active participant
        validateEmailNotAlreadyParticipant(hub.getId(), brandContact.getEmail());
        
        return createNewBrandContactParticipant(hub, brandContact, item.getRole(), accountId, invitationContext);
    }

    // ========================================
    // Private Helper Methods
    // ========================================

    /**
     * Validates internal user request.
     */
    private void validateInternalUserRequest(ParticipantInviteItem item) {
        if (item.getUserId() == null) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, USER_ID_REQUIRED_MESSAGE);
        }
    }

    /**
     * Validates external user request.
     */
    private void validateExternalUserRequest(ParticipantInviteItem item) {
        if (item.getEmail() == null || item.getEmail().trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, EMAIL_REQUIRED_MESSAGE);
        }
        if (item.getName() == null || item.getName().trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, NAME_REQUIRED_MESSAGE);
        }
    }

    /**
     * Validates brand contact request.
     */
    private void validateBrandContactRequest(ParticipantInviteItem item) {
        if (item.getBrandContactId() == null) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, BRAND_CONTACT_ID_REQUIRED_MESSAGE);
        }
    }

    /**
     * Validates that a user is not already a participant.
     */
    private void validateUserNotAlreadyParticipant(Long hubId, Long userId) {
        if (participantRepository.findByUserIdAndHubId(userId, hubId).isPresent()) {
            throw new ConflictException(ErrorCode.PARTICIPANT_ALREADY_EXISTS, PARTICIPANT_ALREADY_EXISTS_MESSAGE);
        }
    }

    /**
     * Validates that an email is not already a participant.
     */
    private void validateEmailNotAlreadyParticipant(Long hubId, String email) {
        if (participantRepository.findByEmailAndHubId(email, hubId).isPresent()) {
            throw new ConflictException(ErrorCode.PARTICIPANT_ALREADY_EXISTS, EMAIL_ALREADY_EXISTS_MESSAGE);
        }
    }

    /**
     * Finds and validates a user.
     */
    private User findAndValidateUser(Long userId, Long accountId) {
        User user = userRepository.findById(userId);
        if (user == null) {
            throw new NotFoundException(ErrorCode.PARTICIPANT_NOT_FOUND, USER_NOT_FOUND_MESSAGE + userId);
        }
        return user;
    }

    /**
     * Finds and validates a brand contact.
     */
    private BrandContact findAndValidateBrandContact(Long brandContactId, Long brandId, Long accountId) {
        return brandContactRepository.findByIdAndBrandIdAndAccountId(brandContactId, brandId, accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.PARTICIPANT_NOT_FOUND,
                        BRAND_CONTACT_NOT_FOUND_MESSAGE + brandContactId));
    }

    /**
     * Finds a removed participant by user ID.
     */
    private HubParticipant findRemovedParticipant(Long hubId, String email) {
        return participantRepository.findByHubIdAndUserIdIncludingRemoved(hubId, email);
    }

    /**
     * Finds a removed participant by email.
     */
    private HubParticipant findRemovedParticipantByEmail(Long hubId, String email) {
        return participantRepository.findByHubIdAndEmailIncludingRemoved(hubId, email);
    }

    /**
     * Reactivates an internal participant.
     */
    private HubParticipantInviteResponse.InvitedParticipant reactivateInternalParticipant(
            HubParticipant participant, HubParticipantRole role, String hubName, InvitationContext invitationContext) {

        boolean reactivated = participantRepository.reactivateParticipant(
                participant.getId(), role, null, null, true);
        if (!reactivated) {
            throw new ConflictException(ErrorCode.INVALID_OPERATION, "Failed to reactivate participant");
        }

        logger.info(REACTIVATED_INTERNAL_PARTICIPANT_LOG, participant.getId(), participant.getHubId());

        // Send invitation notification to reactivated internal user
        if (participant.getUserId() != null) {
            try {
                sendHubInvitationNotification(participant.getUserId(), participant.getHubId(),
                                            hubName, invitationContext.getInviterName());
            } catch (Exception e) {
                logger.warn("Failed to send reactivation notification to user {}: {}", participant.getUserId(), e.getMessage());
            }
        }

        return new HubParticipantInviteResponse.InvitedParticipant(
                participant.getId(), participant.getUserId(), participant.getEmail(), participant.getName(),
                role, false, LocalDateTime.now(), LocalDateTime.now(), "active");
    }

    /**
     * Reactivates an external participant.
     */
    private HubParticipantInviteResponse.InvitedParticipant reactivateExternalParticipant(
            HubParticipant participant, HubParticipantRole role, String hubName, Long accountId) {

        // Generate new magic link for reactivated external participant with hub context
        HubAccessRedirectContext redirectContext = new HubAccessRedirectContext(participant.getHubId(), role.name());
        String token = magicLinkService.createMagicLinkForEmailWithRedirect(participant.getEmail(), accountId, redirectContext);

        boolean reactivated = participantRepository.reactivateParticipantWithoutMagicLink(
                participant.getId(), role, true);
        if (!reactivated) {
            throw new ConflictException(ErrorCode.INVALID_OPERATION, "Failed to reactivate participant");
        }

        sendInvitationEmailAsync(participant.getEmail(), token, hubName, participant.getId());

        // Ensure notification preferences exist for reactivated external participant
        createNotificationPreferencesForExternalParticipant(participant.getEmail());

        // External participant reactivated - notification sent via magic link email
        // Note: External participants don't have user_id, so standard notification system can't be used
        logger.info(REACTIVATED_EXTERNAL_PARTICIPANT_LOG, participant.getId(), participant.getHubId());

        return new HubParticipantInviteResponse.InvitedParticipant(
                participant.getId(), null, participant.getEmail(), participant.getName(),
                role, true, LocalDateTime.now(), null, "pending");
    }

    /**
     * Reactivates a brand contact participant.
     */
    private HubParticipantInviteResponse.InvitedParticipant reactivateBrandContactParticipant(
            HubParticipant participant, HubParticipantRole role, String hubName, Long accountId) {

        // Generate new magic link for reactivated brand contact
        String token = magicLinkService.createMagicLinkForEmail(participant.getEmail(), accountId);

        boolean reactivated = participantRepository.reactivateParticipantWithoutMagicLink(
                participant.getId(), role, true);
        if (!reactivated) {
            throw new ConflictException(ErrorCode.INVALID_OPERATION, "Failed to reactivate participant");
        }

        sendInvitationEmailAsync(participant.getEmail(), token, hubName, participant.getId());

        // Ensure notification preferences exist for reactivated brand contact
        createNotificationPreferencesForExternalParticipant(participant.getEmail());

        logger.info(REACTIVATED_BRAND_CONTACT_PARTICIPANT_LOG, participant.getId(), participant.getHubId());

        return new HubParticipantInviteResponse.InvitedParticipant(
                participant.getId(), null, participant.getEmail(), participant.getName(),
                role, true, LocalDateTime.now(), null, "pending");
    }

    /**
     * Creates a new internal participant.
     */
    private HubParticipantInviteResponse.InvitedParticipant createNewInternalParticipant(
            CollaborationHub hub, User user, HubParticipantRole role, InvitationContext invitationContext) {

        // For now, use the DAO insert method - in production this would be a proper repository method
        HubParticipant participant = new org.jooq.generated.tables.pojos.HubParticipant();
        participant.setHubId(hub.getId());
        participant.setUserId(user.getId());
        participant.setEmail(user.getEmail());
        participant.setName(user.getDisplayName());
        participant.setRole(role);
        participant.setIsExternal(false);
        participant.setInvitedAt(LocalDateTime.now());
        participant.setJoinedAt(LocalDateTime.now()); // Internal users join immediately
        participant.setCreatedAt(LocalDateTime.now());

        participantRepository.insert(participant);

        // Create creator channel if needed
        createCreatorChannelIfNeeded(hub.getId(), participant.getId(), role, user.getDisplayName());

        // Send invitation notification to internal user
        try {
            sendHubInvitationNotification(user.getId(), hub.getId(), hub.getName(), invitationContext.getInviterName());
        } catch (Exception e) {
            logger.warn("Failed to send invitation notification to user {}: {}", user.getId(), e.getMessage());
        }

        return new HubParticipantInviteResponse.InvitedParticipant(
                participant.getId(), user.getId(), user.getEmail(), user.getDisplayName(),
                role, false, participant.getInvitedAt(), participant.getJoinedAt(), "active");
    }

    /**
     * Creates a new external participant.
     */
    private HubParticipantInviteResponse.InvitedParticipant createNewExternalParticipant(
            CollaborationHub hub, ParticipantInviteItem item, Long accountId, InvitationContext invitationContext) {

        String participantName = getParticipantNameFromEmailOrProvided(item);

        // For now, use the DAO insert method - in production this would be a proper repository method
        HubParticipant participant = new org.jooq.generated.tables.pojos.HubParticipant();
        participant.setHubId(hub.getId());
        participant.setEmail(item.getEmail());
        participant.setName(participantName);
        participant.setRole(item.getRole());
        participant.setIsExternal(true);
        participant.setInvitedAt(LocalDateTime.now());
        participant.setCreatedAt(LocalDateTime.now());

        participantRepository.insert(participant);

        // Generate magic link with hub context and send invitation email
        HubAccessRedirectContext redirectContext = new HubAccessRedirectContext(hub.getId(), item.getRole().name());
        String token = magicLinkService.createMagicLinkForEmailWithRedirect(item.getEmail(), accountId, redirectContext);
        sendInvitationEmailAsync(item.getEmail(), token, hub.getName(), participant.getId(), invitationContext);

        // Create creator channel if needed
        createCreatorChannelIfNeeded(hub.getId(), participant.getId(), item.getRole(), participantName);

        // Create default notification preferences for external participant
        createNotificationPreferencesForExternalParticipant(item.getEmail());

        // Send invitation notification to external participant (email only)
        // Note: External participants don't have user_id, so we can't use the standard notification system
        // The magic link email above serves as the invitation notification for external users
        logger.info("External participant {} invited to hub {} - notification sent via magic link email",
                   item.getEmail(), hub.getId());

        return new HubParticipantInviteResponse.InvitedParticipant(
                participant.getId(), null, item.getEmail(), participantName,
                item.getRole(), true, participant.getInvitedAt(), null, "pending");
    }

    /**
     * Creates a new brand contact participant.
     */
    private HubParticipantInviteResponse.InvitedParticipant createNewBrandContactParticipant(
            CollaborationHub hub, BrandContact brandContact, HubParticipantRole role,
            Long accountId, InvitationContext invitationContext) {

        // For now, use the DAO insert method - in production this would be a proper repository method
        HubParticipant participant = new org.jooq.generated.tables.pojos.HubParticipant();
        participant.setHubId(hub.getId());
        participant.setEmail(brandContact.getEmail());
        participant.setName(brandContact.getName());
        participant.setRole(role);
        participant.setIsExternal(true);
        participant.setInvitedAt(LocalDateTime.now());
        participant.setCreatedAt(LocalDateTime.now());

        participantRepository.insert(participant);

        // Generate magic link with hub context and send invitation email
        HubAccessRedirectContext redirectContext = new HubAccessRedirectContext(hub.getId(), role.name());
        String token = magicLinkService.createMagicLinkForEmailWithRedirect(brandContact.getEmail(), accountId, redirectContext);
        sendInvitationEmailAsync(brandContact.getEmail(), token, hub.getName(), participant.getId(), invitationContext);

        // Create creator channel if needed
        createCreatorChannelIfNeeded(hub.getId(), participant.getId(), role, brandContact.getName());

        // Create default notification preferences for brand contact (external participant)
        createNotificationPreferencesForExternalParticipant(brandContact.getEmail());

        // Brand contact participant invited - notification sent via magic link email
        // Note: Brand contacts are external participants without user_id, so standard notification system can't be used
        logger.info("Brand contact {} invited to hub {} - notification sent via magic link email",
                   brandContact.getEmail(), hub.getId());

        return new HubParticipantInviteResponse.InvitedParticipant(
                participant.getId(), null, brandContact.getEmail(), brandContact.getName(),
                role, true, participant.getInvitedAt(), null, "pending");
    }

    /**
     * Gets participant name from email or provided name.
     */
    private String getParticipantNameFromEmailOrProvided(ParticipantInviteItem item) {
        if (item.getName() != null && !item.getName().trim().isEmpty()) {
            return item.getName().trim();
        }

        // Extract name from email if not provided
        String email = item.getEmail();
        if (email != null && email.contains("@")) {
            return email.substring(0, email.indexOf("@"));
        }

        return email;
    }

    /**
     * Creates creator channel if participant is a content creator.
     */
    private void createCreatorChannelIfNeeded(Long hubId, Long participantId, HubParticipantRole role, String participantName) {
        if (role == HubParticipantRole.content_creator) {
            // For now, skip channel creation - this would be implemented in production
            logger.debug("Would create creator channel for participant {} in hub {}", participantId, hubId);
        }
    }

    /**
     * Creates default notification preferences for external participants.
     * This ensures they can receive email notifications and have proper defaults.
     */
    private void createNotificationPreferencesForExternalParticipant(String email) {
        try {
            logger.debug("Creating notification preferences for external participant: {}", email);
            notificationPreferenceRepository.createDefaultPreferencesForEmail(email);
            logger.info("Created default notification preferences for external participant: {}", email);
        } catch (Exception e) {
            logger.warn("Failed to create notification preferences for external participant {}: {}",
                       email, e.getMessage());
        }
    }

    /**
     * Sends invitation email asynchronously.
     */
    @Async
    public void sendInvitationEmailAsync(String email, String token, String hubName, Long participantId) {
        sendInvitationEmailAsync(email, token, hubName, participantId, null);
    }

    /**
     * Sends invitation email asynchronously with context.
     */
    @Async
    public void sendInvitationEmailAsync(String email, String token, String hubName, Long participantId,
                                       InvitationContext invitationContext) {
        try {
            if (invitationContext != null) {
                sendEnhancedInvitationEmail(email, hubName, invitationContext.getAccountName(),
                                          invitationContext.getInviterName(), token);
            } else {
                // For now, just log the email sending - in production this would send actual emails
                logger.info("Would send magic link email to {} for hub {} with token {}",
                           email, hubName, token.substring(0, 8) + "...");
            }
        } catch (Exception e) {
            logger.error("Failed to send invitation email to {} for participant {}: {}",
                        email, participantId, e.getMessage());
        }
    }

    /**
     * Sends enhanced invitation email with context.
     */
    private void sendEnhancedInvitationEmail(String email, String hubName, String accountName,
                                           String inviterName, String token) {
        // For now, use basic email sending - enhanced templates would be implemented in production
        logger.info("Sending invitation email to {} for hub {} from {} at {}",
                   email, hubName, inviterName, accountName);
         emailService.sendMagicLink(email, token, hubName);
    }

    /**
     * Context for invitation emails.
     */
    public static class InvitationContext {
        private final String inviterName;
        private final String accountName;

        public InvitationContext(String inviterName, String accountName) {
            this.inviterName = inviterName;
            this.accountName = accountName;
        }

        public String getInviterName() {
            return inviterName;
        }

        public String getAccountName() {
            return accountName;
        }
    }

    /**
     * Sends hub invitation notification using the unified notification system.
     */
    private void sendHubInvitationNotification(Long userId, Long hubId, String hubName, String inviterName) {
        try {
            // Get user details for recipient creation
            User user = userRepository.findById(userId);
            if (user == null) {
                logger.warn("Cannot send invitation notification - user not found: {}", userId);
                return;
            }

            // Create internal user recipient
            UnifiedNotificationRecipient recipient = UnifiedNotificationRecipient.forInternalUser(userId, user.getEmail(), user.getDisplayName());

            // Create strongly-typed notification
            InviteToHubNotification notification = InviteToHubNotification.builder()
                .recipients(List.of(recipient))
                .inviterName(inviterName)
                .hubTitle(hubName)
                .entityIds(hubId)
                .build();

            // Send notification using unified system
            notificationDispatcherService.dispatch(notification);

        } catch (Exception e) {
            logger.error("Failed to send hub invitation notification to user {}: {}", userId, e.getMessage(), e);
        }
    }
}
