package com.collabhub.be.modules.notifications.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * Production-grade interface for unified notification recipients.
 *
 * <p>This interface provides a unified abstraction for both internal users (with user_id)
 * and external users (email-only) in the notification system. It enables type-safe handling
 * of mixed recipient lists while maintaining clear separation of concerns.</p>
 *
 * <h3>Supported Recipient Types:</h3>
 * <ul>
 *   <li><strong>Internal Users</strong>: {@link UnifiedNotificationRecipient} with user_id - users from user table</li>
 *   <li><strong>External Users</strong>: {@link UnifiedNotificationRecipient} without user_id - hub participants with email-only identification</li>
 * </ul>
 *
 * <h3>Usage Examples:</h3>
 * <pre>
 * // Create internal user recipient
 * NotificationRecipient internal = UnifiedNotificationRecipient.forInternalUser(123L, "<EMAIL>", "<PERSON>");
 *
 * // Create external user recipient
 * NotificationRecipient external = UnifiedNotificationRecipient.forExternalUser("<EMAIL>", "<PERSON>");
 *
 * // Mixed recipient list
 * List&lt;NotificationRecipient&gt; recipients = List.of(internal, external);
 * </pre>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public interface NotificationRecipient {

    /**
     * Determines if this recipient is an internal user with a user_id.
     *
     * @return true if this is an internal user, false for external users
     */
    boolean isInternal();

    /**
     * Gets the email address for this recipient.
     *
     * <p>This is the primary identifier for external users and a secondary
     * identifier for internal users (used for email delivery).</p>
     *
     * @return the email address (never null or empty)
     */
    @NotBlank
    @Email
    @Size(max = 255)
    String email();

    /**
     * Gets the display name for this recipient.
     *
     * <p>Used for personalization in notification messages and email templates.</p>
     *
     * @return the display name (never null or empty)
     */
    @NotBlank
    @Size(max = 100)
    String displayName();

    /**
     * Gets the user ID for internal users.
     *
     * <p>This method should only be called after checking {@link #isInternal()}.
     * For external users, this will throw an {@link UnsupportedOperationException}.</p>
     *
     * <p><strong>Note:</strong> This method is not validated by Bean Validation since external
     * users don't have user IDs. Validation is handled at the implementation level.</p>
     *
     * @return the user ID for internal users
     * @throws UnsupportedOperationException if called on external users
     */
    Long userId();

    /**
     * Gets the recipient type for logging and debugging purposes.
     *
     * @return "INTERNAL" for internal users, "EXTERNAL" for external users
     */
    @NotNull
    default String getRecipientType() {
        return isInternal() ? "INTERNAL" : "EXTERNAL";
    }

    /**
     * Gets a unique identifier for this recipient across both types.
     *
     * <p>Format: "user:{user_id}" for internal users, "email:{email}" for external users.
     * This is useful for logging, caching, and deduplication.</p>
     *
     * @return unique identifier string
     */
    @NotBlank
    default String getUniqueIdentifier() {
        return isInternal() ? "user:" + userId() : "email:" + email();
    }

    /**
     * Validates that this recipient has all required fields populated correctly.
     *
     * @throws IllegalStateException if validation fails
     */
    default void validate() {
        if (email() == null || email().trim().isEmpty()) {
            throw new IllegalStateException("Email cannot be null or empty");
        }
        
        if (displayName() == null || displayName().trim().isEmpty()) {
            throw new IllegalStateException("Display name cannot be null or empty");
        }
        
        if (email().length() > 255) {
            throw new IllegalStateException("Email exceeds maximum length of 255 characters");
        }
        
        if (displayName().length() > 100) {
            throw new IllegalStateException("Display name exceeds maximum length of 100 characters");
        }
        
        // Email format validation (basic check)
        if (!email().contains("@") || !email().contains(".")) {
            throw new IllegalStateException("Invalid email format: " + email());
        }
        
        // Internal user specific validation
        if (isInternal()) {
            try {
                Long userId = userId();
                if (userId == null || userId <= 0) {
                    throw new IllegalStateException("Internal user must have positive user ID");
                }
            } catch (UnsupportedOperationException e) {
                throw new IllegalStateException("Internal user must support getUserId() method");
            }
        }
    }

    /**
     * Creates a string representation suitable for logging.
     *
     * @return string representation with type and identifier
     */
    default String toLogString() {
        return String.format("%s[%s, name=%s]", 
                           getRecipientType(), 
                           isInternal() ? "userId=" + userId() : "email=" + email(),
                           displayName());
    }
}
