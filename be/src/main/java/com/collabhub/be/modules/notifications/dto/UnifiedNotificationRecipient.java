package com.collabhub.be.modules.notifications.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.User;

import java.util.Objects;

/**
 * Production-grade unified implementation of NotificationRecipient.
 *
 * <p>This class represents both internal users (with user_id) and external users (email-only)
 * in a single, unified data structure. It uses email as the universal identifier while
 * maintaining optional user_id for internal users, eliminating the need for separate
 * recipient types and reducing system complexity.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Unified handling of both internal and external users</li>
 *   <li>Email as universal identifier for all notification operations</li>
 *   <li>Optional user_id for internal users (null for external users)</li>
 *   <li>Immutable value object with comprehensive validation</li>
 *   <li>Factory methods for safe construction from various sources</li>
 *   <li>Thread-safe and null-safe operations</li>
 * </ul>
 *
 * <h3>Usage Examples:</h3>
 * <pre>
 * // Create internal user recipient
 * UnifiedNotificationRecipient internal = UnifiedNotificationRecipient.forInternalUser(
 *     123L, "<EMAIL>", "John Doe");
 *
 * // Create external user recipient  
 * UnifiedNotificationRecipient external = UnifiedNotificationRecipient.forExternalUser(
 *     "<EMAIL>", "Jane Smith");
 *
 * // Create from entities
 * UnifiedNotificationRecipient fromUser = UnifiedNotificationRecipient.fromUser(user);
 * UnifiedNotificationRecipient fromParticipant = UnifiedNotificationRecipient.fromHubParticipant(participant);
 * </pre>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public record UnifiedNotificationRecipient(
        Long userId, // Nullable - null for external users, positive for internal users
        @NotBlank @Email @Size(max = 255) String email,
        @NotBlank @Size(max = 100) String displayName) implements NotificationRecipient {

    /**
     * Constructor with comprehensive validation.
     *
     * @param userId      the user ID (null for external users, positive for internal users)
     * @param email       the email address (must be valid email format)
     * @param displayName the display name (must not be blank)
     */
    public UnifiedNotificationRecipient(Long userId,
                                        @NotBlank @Email String email,
                                        @NotBlank String displayName) {
        // Validate userId if present
        if (userId != null && userId <= 0) {
            throw new IllegalArgumentException("User ID must be positive when provided");
        }

        this.userId = userId;
        this.email = Objects.requireNonNull(email, "Email cannot be null").trim().toLowerCase();
        this.displayName = Objects.requireNonNull(displayName, "Display name cannot be null").trim();

        // Validate during construction
        validate();
    }

    // ========================================
    // FACTORY METHODS FOR INTERNAL USERS
    // ========================================

    /**
     * Factory method to create a recipient for an internal user.
     *
     * @param userId      the user ID (must be positive)
     * @param email       the email address (must be valid)
     * @param displayName the display name (must not be blank)
     * @return new UnifiedNotificationRecipient instance for internal user
     * @throws IllegalArgumentException if any parameter is invalid
     */
    public static UnifiedNotificationRecipient forInternalUser(@NotNull @Positive Long userId,
                                                               @NotBlank @Email String email,
                                                               @NotBlank String displayName) {
        Objects.requireNonNull(userId, "User ID cannot be null for internal users");
        return new UnifiedNotificationRecipient(userId, email, displayName);
    }

    /**
     * Factory method to create from a User entity.
     *
     * @param user the user entity (must not be null)
     * @return new UnifiedNotificationRecipient instance for internal user
     * @throws IllegalArgumentException if user is invalid
     */
    public static UnifiedNotificationRecipient fromUser(@NotNull User user) {
        Objects.requireNonNull(user, "User cannot be null");

        if (user.getId() == null || user.getId() <= 0) {
            throw new IllegalArgumentException("User must have a positive ID");
        }

        if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("User must have a valid email address");
        }

        String displayName = determineDisplayNameFromUser(user);
        return new UnifiedNotificationRecipient(user.getId(), user.getEmail(), displayName);
    }

    // ========================================
    // FACTORY METHODS FOR EXTERNAL USERS
    // ========================================

    /**
     * Factory method to create a recipient for an external user.
     *
     * @param email       the email address (must be valid)
     * @param displayName the display name (must not be blank)
     * @return new UnifiedNotificationRecipient instance for external user
     * @throws IllegalArgumentException if any parameter is invalid
     */
    public static UnifiedNotificationRecipient forExternalUser(@NotBlank @Email String email,
                                                               @NotBlank String displayName) {
        return new UnifiedNotificationRecipient(null, email, displayName);
    }

    /**
     * Factory method to create from a HubParticipant entity.
     *
     * <p>Automatically determines if the participant is internal (has user_id) or
     * external (email-only) and creates the appropriate recipient.</p>
     *
     * @param participant the hub participant (must not be null)
     * @return new UnifiedNotificationRecipient instance
     * @throws IllegalArgumentException if participant is invalid
     */
    public static UnifiedNotificationRecipient fromHubParticipant(@NotNull HubParticipant participant) {
        Objects.requireNonNull(participant, "Hub participant cannot be null");

        if (participant.getEmail() == null || participant.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("Hub participant must have a valid email address");
        }

        String displayName = determineDisplayNameFromParticipant(participant);

        // Create internal or external recipient based on user_id presence
        if (participant.getUserId() != null) {
            // Internal participant - validate user_id
            if (participant.getUserId() <= 0) {
                throw new IllegalArgumentException("Hub participant user_id must be positive");
            }
            return new UnifiedNotificationRecipient(participant.getUserId(), participant.getEmail(), displayName);
        } else {
            // External participant
            return new UnifiedNotificationRecipient(null, participant.getEmail(), displayName);
        }
    }

    /**
     * Factory method to create from a HubParticipant with User data for internal participants.
     *
     * <p>This method should be used when you have both HubParticipant and User data
     * for internal participants, ensuring complete and consistent recipient information.</p>
     *
     * @param participant the hub participant (must not be null)
     * @param user the user entity (must not be null for internal participants)
     * @return new UnifiedNotificationRecipient instance
     * @throws IllegalArgumentException if data is inconsistent
     */
    public static UnifiedNotificationRecipient fromHubParticipantWithUser(@NotNull HubParticipant participant,
                                                                          @NotNull User user) {
        Objects.requireNonNull(participant, "Hub participant cannot be null");

        if (participant.getUserId() == null) {
            // External participant - ignore user data and use participant info
            return fromHubParticipant(participant);
        } else {
            // Internal participant - validate consistency and use user data
            Objects.requireNonNull(user, "User cannot be null for internal participants");
            
            if (!participant.getUserId().equals(user.getId())) {
                throw new IllegalArgumentException("HubParticipant user_id does not match User id");
            }
            
            return fromUser(user);
        }
    }

    // ========================================
    // DISPLAY NAME DETERMINATION
    // ========================================

    /**
     * Determines the best display name from user data.
     */
    private static String determineDisplayNameFromUser(@NotNull User user) {
        // Try display name first
        if (user.getDisplayName() != null && !user.getDisplayName().trim().isEmpty()) {
            return user.getDisplayName().trim();
        }

        // Fall back to email prefix
        String email = user.getEmail().trim();
        int atIndex = email.indexOf('@');
        if (atIndex > 0) {
            return email.substring(0, atIndex);
        }

        // Last resort
        return "User " + user.getId();
    }

    /**
     * Determines the best display name from hub participant data.
     */
    private static String determineDisplayNameFromParticipant(@NotNull HubParticipant participant) {
        // Try name first
        if (participant.getName() != null && !participant.getName().trim().isEmpty()) {
            return participant.getName().trim();
        }

        // Fall back to formatted email prefix
        String email = participant.getEmail().trim();
        int atIndex = email.indexOf('@');
        if (atIndex > 0) {
            String prefix = email.substring(0, atIndex);
            return capitalizeAndFormat(prefix);
        }

        // Last resort
        return participant.getUserId() != null ? "User " + participant.getUserId() : "External User";
    }

    /**
     * Formats email prefix into a readable display name.
     */
    private static String capitalizeAndFormat(String prefix) {
        if (prefix == null || prefix.isEmpty()) {
            return "User";
        }

        // Replace common separators with spaces
        String formatted = prefix.replace(".", " ")
                .replace("_", " ")
                .replace("-", " ");

        // Capitalize each word
        String[] words = formatted.split("\\s+");
        StringBuilder result = new StringBuilder();

        for (String word : words) {
            if (!word.isEmpty()) {
                if (result.length() > 0) {
                    result.append(" ");
                }
                result.append(Character.toUpperCase(word.charAt(0)));
                if (word.length() > 1) {
                    result.append(word.substring(1).toLowerCase());
                }
            }
        }

        return result.length() > 0 ? result.toString() : "User";
    }

    // ========================================
    // NOTIFICATION RECIPIENT INTERFACE
    // ========================================

    @Override
    public boolean isInternal() {
        return userId != null;
    }

    @Override
    public Long userId() {
        if (userId == null) {
            throw new UnsupportedOperationException("External users do not have user IDs");
        }
        return userId;
    }

    // ========================================
    // BUILDER PATTERN
    // ========================================

    /**
     * Builder pattern for flexible construction.
     *
     * @return new Builder instance
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder class for flexible UnifiedNotificationRecipient construction.
     */
    public static final class Builder {
        private Long userId;
        private String email;
        private String displayName;

        private Builder() {
        }

        public Builder userId(Long userId) {
            this.userId = userId;
            return this;
        }

        public Builder email(@NotBlank @Email String email) {
            this.email = email;
            return this;
        }

        public Builder displayName(@NotBlank String displayName) {
            this.displayName = displayName;
            return this;
        }

        /**
         * Builds the UnifiedNotificationRecipient with validation.
         *
         * @return new UnifiedNotificationRecipient instance
         * @throws IllegalArgumentException if any required field is missing or invalid
         */
        public UnifiedNotificationRecipient build() {
            if (email == null || email.trim().isEmpty()) {
                throw new IllegalArgumentException("Email is required");
            }
            if (displayName == null || displayName.trim().isEmpty()) {
                throw new IllegalArgumentException("Display name is required");
            }

            return new UnifiedNotificationRecipient(userId, email, displayName);
        }
    }

    @Override
    public String toString() {
        return String.format("UnifiedNotificationRecipient{userId=%s, email='%s', displayName='%s', type=%s}",
                userId, email, displayName, getRecipientType());
    }
}
