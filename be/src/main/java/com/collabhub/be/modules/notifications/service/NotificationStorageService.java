package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.notifications.constants.NotificationConstants;
import com.collabhub.be.modules.notifications.dto.UnifiedNotificationRecipient;
import com.collabhub.be.modules.notifications.dto.NotificationMetadata;
import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import com.collabhub.be.modules.notifications.dto.NotificationStatus;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.dto.NotificationUrgency;
import com.collabhub.be.modules.notifications.exception.NotificationStorageException;
import com.collabhub.be.modules.notifications.repository.NotificationRepository;
import com.collabhub.be.modules.notifications.util.NotificationRecipientUtils;
import org.jooq.JSONB;
import org.jooq.generated.tables.pojos.Notification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * Production-grade service for storing and managing in-app notifications.
 *
 * <p>This service handles notification persistence, status updates, and cleanup using
 * strongly-typed data structures and comprehensive validation. It provides type-safe
 * storage of notification metadata and entity references with proper error handling.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Strongly-typed metadata using {@link NotificationMetadata}</li>
 *   <li>Urgency-based notification creation with {@link NotificationUrgency}</li>
 *   <li>Comprehensive validation and null safety</li>
 *   <li>Efficient bulk notification creation</li>
 *   <li>Proper transaction management</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Service
@Validated
public class NotificationStorageService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationStorageService.class);

    // Constants
    private static final int NOTIFICATION_RETENTION_DAYS = 30;
    private static final int CLEANUP_BATCH_SIZE = 1000;
    private static final int MAX_TITLE_LENGTH = 255;
    private static final int MAX_MESSAGE_LENGTH = 1000;
    private static final String CREATE_NOTIFICATIONS_MESSAGE = "Creating {} in-app notifications of type: {}";
    private static final String CREATED_NOTIFICATIONS_MESSAGE = "Created {} in-app notifications for type: {}";
    private static final String RETRIEVE_NOTIFICATIONS_MESSAGE = "Retrieving {} notifications for user {}";
    private static final String MARK_READ_MESSAGE = "Marking notification as read: id={}, user={}";
    private static final String MARK_READ_SUCCESS_MESSAGE = "Marked notification as read: id={}, user={}";
    private static final String MARK_READ_FAILED_MESSAGE = "Failed to mark notification as read: id={}, user={}";
    private static final String CLEANUP_START_MESSAGE = "Starting cleanup of old notifications (older than {} days)";
    private static final String CLEANUP_COMPLETE_MESSAGE = "Cleaned up {} old notifications";

    private final NotificationRepository notificationRepository;
    private final JwtClaimsService jwtClaimsService;
    private final ObjectMapper objectMapper;

    public NotificationStorageService(NotificationRepository notificationRepository,
                                    JwtClaimsService jwtClaimsService,
                                    ObjectMapper objectMapper) {
        this.notificationRepository = notificationRepository;
        this.jwtClaimsService = jwtClaimsService;
        this.objectMapper = objectMapper;
    }



    /**
     * Creates in-app notifications for multiple users with strongly-typed metadata.
     *
     * <p>This is a convenience method that uses the default urgency level for the
     * notification type. For full control over urgency, use the overloaded method.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipientUserIds the user IDs to create notifications for (must not be empty)
     * @param entityReferences optional entity references (hubId, postId, etc.)
     * @param metadata optional strongly-typed metadata providing additional context
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationStorageException if notification creation fails
     */
    @Transactional
    public void createNotifications(@NotNull @Valid NotificationType type,
                                  @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                  @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                  @NotEmpty Set<Long> recipientUserIds,
                                  EntityReferences entityReferences,
                                  @Valid NotificationMetadata metadata) {

        NotificationUrgency urgency = determineDefaultUrgency(type);
        createNotifications(type, title, message, recipientUserIds, entityReferences, metadata, urgency);
    }

    /**
     * Determines the default urgency level for a notification type.
     *
     * @param type the notification type
     * @return the default urgency level
     */
    private NotificationUrgency determineDefaultUrgency(@NotNull NotificationType type) {
        return NotificationUrgency.getDefaultForType(type);
    }

    /**
     * Creates in-app notifications for multiple users with full urgency and metadata control.
     *
     * @param type the notification type
     * @param title the notification title
     * @param message the notification message
     * @param recipientUserIds the user IDs to create notifications for
     * @param entityReferences optional entity references (hubId, postId, etc.)
     * @param metadata optional strongly-typed metadata
     * @param urgency notification urgency level
     */
    @Transactional
    public void createNotifications(NotificationType type, String title, String message,
                                  Set<Long> recipientUserIds, EntityReferences entityReferences,
                                  NotificationMetadata metadata, NotificationUrgency urgency) {

        logger.debug("Creating {} notifications of type {} with urgency {} for {} users",
                    recipientUserIds.size(), type, urgency, recipientUserIds.size());

        for (Long userId : recipientUserIds) {
            Notification notification = createSingleNotification(
                    userId, type, title, message, entityReferences, metadata, urgency);

            notificationRepository.insert(notification);

            logger.debug("Created notification: user={}, type={}, urgency={}, title={}",
                        userId, type, urgency, title);
        }

        logger.info("Successfully created {} notifications of type {} with urgency {}",
                   recipientUserIds.size(), type, urgency);
    }

    /**
     * Retrieves notifications for the current user with pagination.
     *
     * @param page the page number (0-based)
     * @param size the page size
     * @param unreadOnly whether to return only unread notifications
     * @return list of notifications
     */
    @Transactional(readOnly = true)
    public List<Notification> getUserNotifications(int page, int size, boolean unreadOnly) {
        Long userId = jwtClaimsService.getCurrentUser().getUserId();
        
        logger.debug("Retrieving notifications for user: {} (page={}, size={}, unreadOnly={})", 
                    userId, page, size, unreadOnly);

        List<Notification> notifications = notificationRepository.findByUserIdWithPagination(
                userId, page, size, unreadOnly);

        logger.info("Retrieved {} notifications for user {}", notifications.size(), userId);
        return notifications;
    }

    /**
     * Marks a notification as read.
     *
     * @param notificationId the notification ID
     */
    @Transactional
    public void markAsRead(Long notificationId) {
        Long userId = jwtClaimsService.getCurrentUser().getUserId();
        
        logger.debug("Marking notification as read: id={}, user={}", notificationId, userId);

        boolean updated = notificationRepository.markAsRead(notificationId, userId);
        
        if (updated) {
            logger.info("Marked notification as read: id={}, user={}", notificationId, userId);
        } else {
            logger.warn("Failed to mark notification as read: id={}, user={}", notificationId, userId);
        }
    }

    /**
     * Marks all notifications as read for the current user.
     */
    @Transactional
    public void markAllAsRead() {
        Long userId = jwtClaimsService.getCurrentUser().getUserId();
        
        logger.debug("Marking all notifications as read for user: {}", userId);

        int updatedCount = notificationRepository.markAllAsRead(userId);
        
        logger.info("Marked {} notifications as read for user {}", updatedCount, userId);
    }

    /**
     * Cleans up old notifications to prevent database bloat.
     * Should be called periodically (e.g., daily cron job).
     */
    @Transactional
    public void cleanupOldNotifications() {
        logger.debug("Starting cleanup of old notifications (older than {} days)", NOTIFICATION_RETENTION_DAYS);

        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(NOTIFICATION_RETENTION_DAYS);
        int deletedCount = notificationRepository.deleteOldNotifications(cutoffDate, CLEANUP_BATCH_SIZE);

        logger.info("Cleaned up {} old notifications", deletedCount);
    }



    /**
     * Creates a single notification entity with full urgency and metadata control.
     */
    private Notification createSingleNotification(Long userId, NotificationType type, String title,
                                                String message, EntityReferences entityReferences,
                                                NotificationMetadata metadata, NotificationUrgency urgency) {
        Notification notification = new Notification();
        notification.setUserId(userId);
        notification.setType(type.toJooqEnum());
        notification.setUrgency(urgency.toJooqEnum());
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setStatus(NotificationStatus.UNREAD.toJooqEnum());
        notification.setCreatedAt(LocalDateTime.now());

        // Set entity references if provided
        if (entityReferences != null) {
            notification.setCollaborationHubId(entityReferences.getHubId());
            notification.setPostId(entityReferences.getPostId());
            notification.setCommentId(entityReferences.getCommentId());
            notification.setChatChannelId(entityReferences.getChatChannelId());
            notification.setBriefId(entityReferences.getBriefId());
        }

        // Set metadata if provided
        if (metadata != null && metadata.hasContent()) {
            // Convert strongly-typed metadata to JSONB format
            notification.setMetadata(convertMetadataToJsonb(metadata));
        }

        return notification;
    }

    /**
     * Converts strongly-typed NotificationMetadata to JSONB format for database storage.
     *
     * @param metadata the strongly-typed metadata to convert
     * @return JSONB representation suitable for database storage
     */
    private JSONB convertMetadataToJsonb(NotificationMetadata metadata) {
        if (metadata == null || !metadata.hasContent()) {
            return null;
        }

        try {
            String jsonString = objectMapper.writeValueAsString(metadata);
            return JSONB.valueOf(jsonString);
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize NotificationMetadata to JSONB: {}", e.getMessage(), e);
            return null;
        }
    }

    // ========================================
    // UNIFIED MIXED RECIPIENT METHODS
    // ========================================

    /**
     * Creates notifications for mixed recipients (internal and external users).
     *
     * <p>This is the primary method for the unified notification system, supporting both
     * internal users (with user_id) and external users (email-only).</p>
     */
    @Transactional
    public void createMixedNotifications(@NotNull @Valid NotificationType type,
                                       @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                       @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                       @NotEmpty List<NotificationRecipient> recipients,
                                       EntityReferences entityReferences,
                                       @Valid NotificationMetadata metadata) {

        NotificationUrgency urgency = determineDefaultUrgency(type);
        createMixedNotifications(type, title, message, recipients, entityReferences, metadata, urgency);
    }

    /**
     * Creates notifications for mixed recipients with full urgency control.
     */
    @Transactional
    public void createMixedNotifications(@NotNull @Valid NotificationType type,
                                       @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                       @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                       @NotEmpty List<NotificationRecipient> recipients,
                                       EntityReferences entityReferences,
                                       @Valid NotificationMetadata metadata,
                                       @NotNull @Valid NotificationUrgency urgency) {

        validateMixedCreationParameters(type, title, message, recipients, urgency);
        logger.debug(CREATE_NOTIFICATIONS_MESSAGE, recipients.size(), type);

        List<Notification> notifications = createMixedNotificationEntities(recipients, type, title, message,
                                                                          entityReferences, metadata, urgency);

        int createdCount = notificationRepository.bulkCreateMixedNotifications(notifications);
        logger.info(CREATED_NOTIFICATIONS_MESSAGE, createdCount, type);
    }

    /**
     * Creates notification entities for mixed recipients.
     */
    private List<Notification> createMixedNotificationEntities(List<NotificationRecipient> recipients,
                                                              NotificationType type, String title, String message,
                                                              EntityReferences entityReferences, NotificationMetadata metadata,
                                                              NotificationUrgency urgency) {

        List<Notification> notifications = new java.util.ArrayList<>();

        for (NotificationRecipient recipient : recipients) {
            Notification notification = createMixedNotificationEntity(recipient, type, title, message,
                                                                     entityReferences, metadata, urgency);
            notifications.add(notification);
        }

        return notifications;
    }

    /**
     * Creates a single notification entity for any recipient type.
     */
    private Notification createMixedNotificationEntity(NotificationRecipient recipient,
                                                     NotificationType type, String title, String message,
                                                     EntityReferences entityReferences, NotificationMetadata metadata,
                                                     NotificationUrgency urgency) {

        Notification notification = new Notification();

        // Set recipient information based on type
        if (recipient.isInternal()) {
            notification.setUserId(recipient.userId());
            notification.setEmail(null); // Internal users use user_id
        } else {
            notification.setUserId(null); // External users use email
            notification.setEmail(recipient.email());
        }

        // Set notification data
        notification.setType(type.toJooqEnum());
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setUrgency(urgency.toJooqEnum());
        notification.setStatus(NotificationStatus.UNREAD.toJooqEnum());
        notification.setCreatedAt(LocalDateTime.now());

        // Set entity references and metadata using existing methods
        setEntityReferences(notification, entityReferences);
        setMetadata(notification, metadata);

        return notification;
    }

    /**
     * Sets entity references on a notification.
     */
    private void setEntityReferences(Notification notification, EntityReferences entityReferences) {
        if (entityReferences != null) {
            notification.setCollaborationHubId(entityReferences.getHubId());
            notification.setPostId(entityReferences.getPostId());
            notification.setCommentId(entityReferences.getCommentId());
            notification.setChatChannelId(entityReferences.getChatChannelId());
            notification.setBriefId(entityReferences.getBriefId());
        }
    }

    /**
     * Sets metadata on a notification.
     */
    private void setMetadata(Notification notification, NotificationMetadata metadata) {
        if (metadata != null && metadata.hasContent()) {
            notification.setMetadata(convertMetadataToJsonb(metadata));
        }
    }

    /**
     * Validates mixed notification creation parameters.
     */
    private void validateMixedCreationParameters(@NotNull NotificationType type,
                                               @NotBlank String title,
                                               @NotBlank String message,
                                               @NotEmpty List<NotificationRecipient> recipients,
                                               @NotNull NotificationUrgency urgency) {

        validateCreationParameters(type, title, message, Set.of(), urgency); // Basic validation

        if (recipients.isEmpty()) {
            throw new IllegalArgumentException("Recipients list cannot be empty");
        }

        // Validate all recipients
        NotificationRecipientUtils.validateRecipients(recipients);
    }

    /**
     * Data class for entity references in notifications.
     */
    public static class EntityReferences {
        private Long hubId;
        private Long postId;
        private Long commentId;
        private Long chatChannelId;
        private Long briefId;

        public EntityReferences() {}

        public static EntityReferences hub(Long hubId) {
            EntityReferences refs = new EntityReferences();
            refs.hubId = hubId;
            return refs;
        }

        public static EntityReferences post(Long hubId, Long postId) {
            EntityReferences refs = new EntityReferences();
            refs.hubId = hubId;
            refs.postId = postId;
            return refs;
        }

        public static EntityReferences comment(Long hubId, Long postId, Long commentId) {
            EntityReferences refs = new EntityReferences();
            refs.hubId = hubId;
            refs.postId = postId;
            refs.commentId = commentId;
            return refs;
        }

        public static EntityReferences chat(Long hubId, Long chatChannelId) {
            EntityReferences refs = new EntityReferences();
            refs.hubId = hubId;
            refs.chatChannelId = chatChannelId;
            return refs;
        }

        public static EntityReferences brief(Long hubId, Long briefId) {
            EntityReferences refs = new EntityReferences();
            refs.hubId = hubId;
            refs.briefId = briefId;
            return refs;
        }

        // Legacy methods for backward compatibility (deprecated)
        @Deprecated
        public static EntityReferences post(Long postId) {
            EntityReferences refs = new EntityReferences();
            refs.postId = postId;
            return refs;
        }

        @Deprecated
        public static EntityReferences comment(Long commentId) {
            EntityReferences refs = new EntityReferences();
            refs.commentId = commentId;
            return refs;
        }

        @Deprecated
        public static EntityReferences chat(Long chatChannelId) {
            EntityReferences refs = new EntityReferences();
            refs.chatChannelId = chatChannelId;
            return refs;
        }

        @Deprecated
        public static EntityReferences brief(Long briefId) {
            EntityReferences refs = new EntityReferences();
            refs.briefId = briefId;
            return refs;
        }

        // Getters and setters
        public Long getHubId() { return hubId; }
        public void setHubId(Long hubId) { this.hubId = hubId; }
        
        public Long getPostId() { return postId; }
        public void setPostId(Long postId) { this.postId = postId; }
        
        public Long getCommentId() { return commentId; }
        public void setCommentId(Long commentId) { this.commentId = commentId; }
        
        public Long getChatChannelId() { return chatChannelId; }
        public void setChatChannelId(Long chatChannelId) { this.chatChannelId = chatChannelId; }

        public Long getBriefId() { return briefId; }
        public void setBriefId(Long briefId) { this.briefId = briefId; }
    }

    // ========================================
    // PRIVATE VALIDATION METHODS
    // ========================================

    /**
     * Validates creation parameters for consistency and business rules.
     *
     * @param type the notification type
     * @param title the notification title
     * @param message the notification message
     * @param recipientUserIds the recipient user IDs (can be empty for mixed validation)
     * @param urgency the notification urgency
     * @throws IllegalArgumentException if validation fails
     */
    private void validateCreationParameters(@NotNull NotificationType type,
                                          @NotBlank String title,
                                          @NotBlank String message,
                                          @NotNull Set<Long> recipientUserIds,
                                          @NotNull NotificationUrgency urgency) {

        if (title.length() > NotificationConstants.Validation.MAX_TITLE_LENGTH) {
            throw new IllegalArgumentException("Title exceeds maximum length of " +
                NotificationConstants.Validation.MAX_TITLE_LENGTH + " characters");
        }

        if (message.length() > NotificationConstants.Validation.MAX_MESSAGE_LENGTH) {
            throw new IllegalArgumentException("Message exceeds maximum length of " +
                NotificationConstants.Validation.MAX_MESSAGE_LENGTH + " characters");
        }

        // Validate user IDs if provided
        if (recipientUserIds != null && recipientUserIds.stream().anyMatch(id -> id == null || id <= 0)) {
            throw new IllegalArgumentException("All recipient user IDs must be positive");
        }
    }
}
