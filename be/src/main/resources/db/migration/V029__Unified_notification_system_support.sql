-- Migration V029: Unified notification system support for internal and external users
-- This migration enables the notification system to handle both:
-- - Internal users: identified by user_id (references user table)
-- - External users: identified by email only (hub participants)

-- ========================================
-- STEP 1: Update notification table
-- ========================================

-- Add email column for external user support
ALTER TABLE notification
ADD COLUMN email VARCHAR(255);

-- Make user_id nullable to support external users
ALTER TABLE notification
ALTER COLUMN user_id DROP NOT NULL;

-- Add constraint: either user_id OR email must be provided (not both, not neither)
ALTER TABLE notification
ADD CONSTRAINT chk_notification_recipient_type
CHECK (
    (user_id IS NOT NULL AND email IS NULL) OR 
    (user_id IS NULL AND email IS NOT NULL)
);

-- Add constraint: email format validation for external users
ALTER TABLE notification
ADD CONSTRAINT chk_notification_email_format
CHECK (
    email IS NULL OR 
    (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' AND LENGTH(email) <= 255)
);

-- ========================================
-- STEP 2: Update notification_batch_queue table
-- ========================================

-- Add email column for external user support
ALTER TABLE notification_batch_queue
ADD COLUMN email VARCHAR(255);

-- Make user_id nullable to support external users
ALTER TABLE notification_batch_queue
ALTER COLUMN user_id DROP NOT NULL;


-- Add constraint: email format validation for external users
ALTER TABLE notification_batch_queue
ADD CONSTRAINT chk_batch_queue_email_format
CHECK (
    email IS NULL OR 
    (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' AND LENGTH(email) <= 255)
);

-- ========================================
-- STEP 3: Create performance indexes
-- ========================================

-- Indexes for external user notification queries
CREATE INDEX idx_notification_email ON notification(email) WHERE user_id IS NULL;
CREATE INDEX idx_notification_email_status ON notification(email, status) WHERE user_id IS NULL;
CREATE INDEX idx_notification_email_created_at ON notification(email, created_at DESC) WHERE user_id IS NULL;
CREATE INDEX idx_notification_email_type ON notification(email, type) WHERE user_id IS NULL;

-- Indexes for external user batch queue queries
CREATE INDEX idx_batch_queue_email ON notification_batch_queue(email) WHERE user_id IS NULL;
CREATE INDEX idx_batch_queue_email_status ON notification_batch_queue(email, status) WHERE user_id IS NULL;
CREATE INDEX idx_batch_queue_email_batch_window ON notification_batch_queue(email, batch_window_start) WHERE user_id IS NULL;

-- Composite indexes for mixed queries (both internal and external)
CREATE INDEX idx_notification_recipient_status ON notification(COALESCE(user_id::text, email), status);
CREATE INDEX idx_notification_recipient_created_at ON notification(COALESCE(user_id::text, email), created_at DESC);

-- ========================================
-- STEP 4: Update table comments
-- ========================================

COMMENT ON COLUMN notification.user_id IS 'User ID for internal users (null for external users identified by email)';
COMMENT ON COLUMN notification.email IS 'Email address for external users (null for internal users identified by user_id)';

COMMENT ON COLUMN notification_batch_queue.user_id IS 'User ID for internal users (null for external users identified by email)';
COMMENT ON COLUMN notification_batch_queue.email IS 'Email address for external users (null for internal users identified by user_id)';

-- Update table comments to reflect unified support
COMMENT ON TABLE notification IS 'Unified notification storage supporting both internal users (user_id) and external users (email)';
COMMENT ON TABLE notification_batch_queue IS 'Unified notification batching queue supporting both internal users (user_id) and external users (email)';

-- ========================================
-- STEP 5: Data validation
-- ========================================

-- Ensure all existing notifications have user_id (should be true for current data)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM notification 
        WHERE user_id IS NULL AND email IS NULL
    ) THEN
        RAISE EXCEPTION 'Migration failed: Found notifications without user_id or email';
    END IF;
    
    IF EXISTS (
        SELECT 1 FROM notification_batch_queue 
        WHERE user_id IS NULL AND email IS NULL
    ) THEN
        RAISE EXCEPTION 'Migration failed: Found batch queue entries without user_id or email';
    END IF;
END $$;

-- ========================================
-- STEP 6: Create helper functions for unified queries
-- ========================================

-- Function to get recipient identifier (user_id or email) for unified queries
CREATE OR REPLACE FUNCTION get_notification_recipient_id(notification_row notification)
RETURNS TEXT AS $$
BEGIN
    IF notification_row.user_id IS NOT NULL THEN
        RETURN 'user:' || notification_row.user_id::text;
    ELSE
        RETURN 'email:' || notification_row.email;
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to check if a notification belongs to a specific recipient
CREATE OR REPLACE FUNCTION notification_belongs_to_recipient(
    notification_row notification,
    recipient_user_id BIGINT DEFAULT NULL,
    recipient_email VARCHAR DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    IF recipient_user_id IS NOT NULL THEN
        RETURN notification_row.user_id = recipient_user_id;
    ELSIF recipient_email IS NOT NULL THEN
        RETURN notification_row.email = recipient_email;
    ELSE
        RETURN FALSE;
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

COMMENT ON FUNCTION get_notification_recipient_id(notification) IS 'Helper function to get unified recipient identifier for notifications';
COMMENT ON FUNCTION notification_belongs_to_recipient(notification, BIGINT, VARCHAR) IS 'Helper function to check notification ownership for unified recipient types';
